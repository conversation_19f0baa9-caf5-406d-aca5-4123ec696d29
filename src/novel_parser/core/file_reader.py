import hashlib
from pathlib import Path


class FileReader:
    @staticmethod
    def read_content_by_offset(file_path: Path, start_offset: int, end_offset: int) -> str:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            f.seek(start_offset)
            content_bytes = end_offset - start_offset
            content = f.read(content_bytes)
            return content.strip()

    @staticmethod
    def read_full_content(file_path: Path) -> str:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            return f.read()

    @staticmethod
    def find_byte_offsets(content: str, chapter_positions: list) -> list:
        lines = content.split('\n')
        chapters_with_offsets = []
        content_bytes = content.encode('utf-8')

        for i, (line_num, chapter_title) in enumerate(chapter_positions):
            start_line = line_num + 1
            start_offset = len('\n'.join(lines[:start_line]).encode('utf-8'))

            if i < len(chapter_positions) - 1:
                end_line = chapter_positions[i + 1][0]
                end_offset = len('\n'.join(lines[:end_line]).encode('utf-8'))
            else:
                end_offset = len(content_bytes)

            chapters_with_offsets.append({
                'title': chapter_title,
                'start_offset': start_offset,
                'end_offset': end_offset,
                'line_start': start_line,
                'line_end': end_line if i < len(chapter_positions) - 1 else len(lines)
            })

        return chapters_with_offsets

    @staticmethod
    def calculate_file_hash(file_path: Path) -> str:
        """Calculate SHA256 hash of file content"""
        hash_sha256 = hashlib.sha256()
        with open(file_path, 'rb') as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_sha256.update(chunk)
        return hash_sha256.hexdigest()
