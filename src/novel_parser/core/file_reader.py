from pathlib import Path


class FileReader:
    @staticmethod
    def read_content_by_offset(file_path: Path, start_offset: int, end_offset: int) -> str:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            f.seek(start_offset)
            content_bytes = end_offset - start_offset
            content = f.read(content_bytes)
            return content

    @staticmethod
    def read_full_content(file_path: Path) -> str:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            return f.read()

    @staticmethod
    def find_byte_offsets(content: str, chapter_positions: list) -> list:
        lines = content.split('\n')
        chapters_with_offsets = []

        # 计算每行的累积字节偏移
        line_offsets = [0]
        for line in lines:
            # 每行加上换行符的字节长度
            line_bytes = len((line + '\n').encode('utf-8'))
            line_offsets.append(line_offsets[-1] + line_bytes)

        for i, (line_num, chapter_title) in enumerate(chapter_positions):
            # 章节内容从标题行开始（包含标题）
            start_offset = line_offsets[line_num]

            if i < len(chapter_positions) - 1:
                # 下一章节的开始位置（不包含下一章节标题）
                next_line_num = chapter_positions[i + 1][0]
                end_offset = line_offsets[next_line_num]
            else:
                # 最后一章到文件末尾
                end_offset = len(content.encode('utf-8'))

            chapters_with_offsets.append({
                'title': chapter_title,
                'start_offset': start_offset,
                'end_offset': end_offset,
                'line_start': line_num + 1,  # 1-based line number
                'line_end': chapter_positions[i + 1][0] if i < len(chapter_positions) - 1 else len(lines)
            })

        return chapters_with_offsets

