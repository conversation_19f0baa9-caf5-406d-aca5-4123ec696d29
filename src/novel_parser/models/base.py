from pydantic import BaseModel
from typing import Optional


class ChapterInfo(BaseModel):
    id: int
    title: str
    index: int


class NovelInfo(BaseModel):
    id: int
    title: str
    author: Optional[str] = None
    file_path: str
    chapter_count: int
    cover_url: str = "/static/book_cover.jpg"


class NovelChapters(BaseModel):
    id: int
    title: str
    chapter_count: int
    chapters: list[ChapterInfo]


class ChapterContent(BaseModel):
    id: int
    title: str
    content: str
    index: int
    novel_id: int
    novel_title: str


class SearchResponse(BaseModel):
    results: list[NovelInfo]


class NovelMetadata(BaseModel):
    title: str
    author: Optional[str] = None
    file_path: str
    file_hash: str
    chapter_count: int
    chapters: list['ChapterMetadata'] = []


class ChapterMetadata(BaseModel):
    title: str
    start_offset: int
    end_offset: int
    chapter_index: int


NovelMetadata.model_rebuild()
