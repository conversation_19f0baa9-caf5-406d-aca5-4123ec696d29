import re
from pathlib import Path
import ebooklib
from ebooklib import epub
from bs4 import BeautifulSoup

from ..models.base import NovelMetadata, ChapterMetadata


class EpubParser:
    def parse_file(self, file_path: Path) -> NovelMetadata | None:
        file_path = Path(file_path)
        if not file_path.exists():
            return None

        book = epub.read_epub(str(file_path))

        title, author = self._extract_metadata(book, file_path)
        chapters = self._extract_chapters_with_offsets(book)

        return NovelMetadata(
            title=title,
            author=author,
            file_path=str(file_path),
            chapter_count=len(chapters),
            chapters=chapters
        )

    def _extract_metadata(self, book: epub.EpubBook, file_path: Path) -> tuple[str, str | None]:
        title_meta = book.get_metadata('DC', 'title')
        title = title_meta[0][0] if title_meta else file_path.stem

        author_meta = book.get_metadata('DC', 'creator')
        author = author_meta[0][0] if author_meta else None

        if not author:
            author_pattern = re.compile(r'(.+)\s作者：(.+)')
            match = author_pattern.match(file_path.stem)
            if match:
                title = match.group(1).strip()
                author = match.group(2).strip()

        return title, author

    def _extract_chapters_with_offsets(self, book: epub.EpubBook) -> list[ChapterMetadata]:
        chapters = []
        current_offset = 0
        chapter_index = 0

        for item in book.get_items():
            if item.get_type() == ebooklib.ITEM_DOCUMENT:
                soup = BeautifulSoup(item.get_content(), 'html.parser')
                title_tag = soup.find(['h1', 'h2', 'h3', 'h4', 'h5', 'h6'])
                title = title_tag.get_text().strip() if title_tag else f"第{chapter_index + 1}章"

                content = self._clean_html_content(item.get_content())
                content_bytes = len(content.encode('utf-8'))

                chapters.append(ChapterMetadata(
                    title=title,
                    start_offset=current_offset,
                    end_offset=current_offset + content_bytes,
                    chapter_index=chapter_index
                ))
                current_offset += content_bytes
                chapter_index += 1

    def _clean_html_content(self, html_content: bytes) -> str:
        soup = BeautifulSoup(html_content, 'html.parser')

        for script in soup(["script", "style"]):
            script.decompose()

        text = soup.get_text()
        lines = (line.strip() for line in text.splitlines())
        chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
        text = ' '.join(chunk for chunk in chunks if chunk)

        return text




