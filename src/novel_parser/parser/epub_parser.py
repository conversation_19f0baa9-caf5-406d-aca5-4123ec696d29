import re
import tempfile
from pathlib import Path
import ebooklib
from ebooklib import epub
from bs4 import BeautifulSoup

from ..models.base import NovelMetadata, ChapterMetadata


class EpubParser:
    def parse_file(self, file_path: Path) -> NovelMetadata | None:
        file_path = Path(file_path)
        if not file_path.exists():
            return None

        book = epub.read_epub(str(file_path))

        title, author = self._extract_metadata(book, file_path)
        chapters, temp_file_path = self._extract_chapters_with_temp_file(book)

        return NovelMetadata(
            title=title,
            author=author,
            file_path=str(temp_file_path),  # 使用临时文件路径
            chapter_count=len(chapters),
            chapters=chapters
        )

    def _extract_metadata(self, book: epub.EpubBook, file_path: Path) -> tuple[str, str | None]:
        title_meta = book.get_metadata('DC', 'title')
        title = title_meta[0][0] if title_meta else file_path.stem

        author_meta = book.get_metadata('DC', 'creator')
        author = author_meta[0][0] if author_meta else None

        if not author:
            author_pattern = re.compile(r'(.+)\s作者：(.+)')
            match = author_pattern.match(file_path.stem)
            if match:
                title = match.group(1).strip()
                author = match.group(2).strip()

        return title, author

    def _extract_chapters_with_temp_file(self, book: epub.EpubBook) -> tuple[list[ChapterMetadata], Path]:
        chapters = []
        all_content = ""
        chapter_index = 0

        # 提取所有章节内容
        for item in book.get_items():
            if item.get_type() == ebooklib.ITEM_DOCUMENT:
                soup = BeautifulSoup(item.get_content(), 'html.parser')
                title_tag = soup.find(['h1', 'h2', 'h3', 'h4', 'h5', 'h6'])
                title = title_tag.get_text().strip() if title_tag else f"第{chapter_index + 1}章"

                content = self._clean_html_content(item.get_content())

                # 章节内容：标题 + 换行 + 内容
                full_content = f"{title}\n{content}"

                # 计算当前章节在合并文件中的偏移量
                start_offset = len(all_content.encode('utf-8'))
                all_content += full_content
                end_offset = len(all_content.encode('utf-8'))

                chapters.append(ChapterMetadata(
                    title=title,
                    start_offset=start_offset,
                    end_offset=end_offset,
                    chapter_index=chapter_index
                ))

                # 章节间添加分隔符（除了最后一章）
                if chapter_index < len(list(book.get_items_of_type(ebooklib.ITEM_DOCUMENT))) - 1:
                    all_content += "\n\n"

                chapter_index += 1

        # 创建临时文件存储所有内容
        temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8')
        temp_file.write(all_content)
        temp_file.close()

        return chapters, Path(temp_file.name)

    def _clean_html_content(self, html_content: bytes) -> str:
        soup = BeautifulSoup(html_content, 'html.parser')

        # 移除脚本和样式标签
        for script in soup(["script", "style"]):
            script.decompose()

        # 获取文本内容，保持段落结构
        text = soup.get_text()

        # 清理文本，保持换行结构
        lines = []
        for line in text.splitlines():
            line = line.strip()
            if line:  # 只保留非空行
                lines.append(line)

        # 用换行符连接，保持段落结构
        return '\n'.join(lines)




