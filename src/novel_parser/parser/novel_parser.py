import re
from pathlib import Path
from ..core.file_reader import FileReader
from ..models.base import NovelMetadata, ChapterMetadata


class NovelParser:
    CHAPTER_PATTERNS = [
        r'^第[\d〇零一二两三四五六七八九十百千万壹贰叁肆伍陆柒捌玖拾佰仟]+[章节回卷].{0,30}$',
        r'^[序终尾楔引前后][章言声子记].{0,30}$',
        r'^(正文|番外).{0,30}$',
        r'^[上中下外][部篇卷].{0,30}$',
        r'^\d{1,4}[^\.：&].{0,30}$',
        r'^Chapter.{0,30}$',
        r'^[☆★].{0,30}$',
        r'^卷[\d〇零一二两三四五六七八九十百千万壹贰叁肆伍陆柒捌玖拾佰仟]+.{0,30}$',
    ]

    def parse_file(self, file_path: Path) -> NovelMetadata | None:
        file_path = Path(file_path)
        if not file_path.exists():
            return None

        content = FileReader.read_full_content(file_path)
        if not content:
            return None

        novel_title, author = self._extract_title_author(file_path)
        chapters = self._extract_chapters_with_offsets(content)
        file_hash = FileReader.calculate_file_hash(file_path)

        return NovelMetadata(
            title=novel_title,
            author=author,
            file_path=str(file_path),
            file_hash=file_hash,
            chapter_count=len(chapters),
            chapters=chapters
        )

    def _extract_title_author(self, file_path: Path) -> tuple[str, str | None]:
        file_name = file_path.stem
        author_pattern = re.compile(r'(.+)\s作者：(.+)')
        match = author_pattern.match(file_name)

        if match:
            return match.group(1).strip(), match.group(2).strip()
        return file_name, None

    def _extract_chapters_with_offsets(self, content: str) -> list[ChapterMetadata]:
        lines = content.split('\n')
        chapter_positions = []

        for i, line in enumerate(lines):
            line_clean = line.strip().replace(' ', '')
            # Skip empty lines and check if the line matches any of our chapter patterns
            if line_clean and any(re.fullmatch(pattern, line_clean) for pattern in self.CHAPTER_PATTERNS):
                chapter_positions.append((i, line.strip()))

        if not chapter_positions:  # If no chapters found, treat the entire content as a single chapter
            return [ChapterMetadata(
                title='全文',
                start_offset=0,
                end_offset=len(content.encode('utf-8')),
                chapter_index=0
            )]

        if chapter_positions[0][0] > 0:  # If the first chapter doesn't start at line 0
            # Add a chapter for the content before the first chapter
            chapters = FileReader.find_byte_offsets(content, chapter_positions)
            chapters.insert(0, {
                'title': '简介',
                'start_offset': 0,
                'end_offset': chapters[0]['start_offset'],
                'line_start': 0+1,
                'line_end': chapter_positions[0][0]
            })
        else:
            chapters = FileReader.find_byte_offsets(content, chapter_positions)

        return [
            ChapterMetadata(
                title=chapter_info['title'],
                start_offset=chapter_info['start_offset'],
                end_offset=chapter_info['end_offset'],
                chapter_index=i
            )
            for i, chapter_info in enumerate(chapters)
        ]
